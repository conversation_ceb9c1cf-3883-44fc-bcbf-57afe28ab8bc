from pathlib import Path
from typing import Any, Dict
from fastapi import FastAPI, Form, HTTPException, Query, UploadFile, logger
from pydantic import BaseModel
from starlette.middleware.cors import CORSMiddleware




# 创建 FastAPI 应用实例
base_dir = Path(__file__).parent
app = FastAPI()
# 定义一个简单的 GET 路由
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
@app.get("/")
async def read_root():
    return {"message": "Hello, World!"}

# 另一个示例路由，带有路径参数
@app.get("/items/{item_id}")
async def read_item(item_id: int, q: str = None):
    return {"item_id": item_id, "q": q}

@app.post("/gpt/api/upload/gpt/image")
async def upload(prefix:str=Form(default="imgs"),file_name:str=Form(default=""),url:str=Form(default=""),data:UploadFile=None):
    """
    上传文件
    url: 文件url
    pre: 文件前缀
    file_name: 文件名
    """
    from control.upload import upload_file
    resp = {
            "code": 0,
            "msg": "success",
            "data": {
                "url": "https://file.302.ai/gpt/imgs/20241226/29fa565918e54301a5ba60cb46c0894d.webp"
            }
            }
    file_name = file_name or data.filename
    data =  await upload_file(prefix,file_name,url=url,data=data)
    resp["data"] = data
    return resp


class DeleteRequest(BaseModel):
    """删除请求模型"""
    file_path: str
    
    class Config:
        schema_extra = {
            "example": {
                "file_path": "imgs/example.jpg"
            }
        }

class DeleteResponse(BaseModel):
    """删除响应模型"""
    success: bool
    message: str
    details: Dict[str, Any]

@app.delete("/file", response_model=DeleteResponse)
async def delete_file_endpoint(request: DeleteRequest):
    """
    删除文件接口
    
    支持的文件路径格式：
    - "gpt/imgs/filename.jpg" (完整路径)
    """
    from control.upload import delete_file
    try:
        print(f"Received delete request for file: {request.file_path}")
        
        # 执行删除操作
        result = await delete_file(request.file_path)
        
        if result["overall_success"]:
            return DeleteResponse(
                success=True,
                message="文件删除成功",
                details=result
            )
        else:
            # 部分成功或完全失败
            success_parts = []
            failed_parts = []
            
            if result["oss_success"]:
                success_parts.append("OSS")
            else:
                failed_parts.append("OSS")
                
            if result["blob_success"]:
                success_parts.append("Blob Storage")
            else:
                failed_parts.append("Blob Storage")
            
            message = ""
            if success_parts:
                message += f"成功删除: {', '.join(success_parts)}. "
            if failed_parts:
                message += f"删除失败: {', '.join(failed_parts)}."
            
            return DeleteResponse(
                success=len(success_parts) > 0,  # 至少有一个成功就算部分成功
                message=message.strip(),
                details=result
            )
            
    except ValueError as e:
        print(f"Invalid file path: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        print(f"Unexpected error during file deletion: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")



@app.get("/blob/sas/url",summary="生成blob的sas url")
async def blob_sas_url(pre:str=Query(default="imgs"),file_name:str=Query(default="")):
    from control.upload import build_blob_sas_url

    sas_url, headers = await build_blob_sas_url(pre,file_name)
    return {"code":0,"msg":"success","data":{"sas_url": sas_url,"headers":headers}}

@app.get("/oss/sas/url",summary="生成oss的sas url")
async def oss_sas_url(pre:str=Query(default="imgs"),file_name:str=Query(default="")):
    from control.upload import build_oss_sas_url
    sas_url, headers = await build_oss_sas_url(pre,file_name,headers={'content_type': 'text/plain'})
    return {"code":0,"msg":"success","data":{"sas_url": sas_url,"headers":headers}}

@app.on_event("startup")
async def startup():
    from utils import load_yaml_config,Tools
    Tools.conf = await load_yaml_config(base_dir/"config"/"setting.yaml")



if __name__ == "__main__":
    import uvicorn
    # 通过 Uvicorn 启动应用
    uvicorn.run(app, host="0.0.0.0", port=8000)
