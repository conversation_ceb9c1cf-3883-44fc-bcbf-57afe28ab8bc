"""
直接URL上传功能使用示例

这个功能允许OSS和Azure Blob Storage直接从URL拉取文件，
而不需要先下载到本地，可以节省带宽和时间。
"""

import asyncio
from control.upload import upload_file

async def example_direct_url_upload():
    """直接URL上传示例"""
    
    # 示例1：基本的直接URL上传
    result = await upload_file(
        prefix="images",
        file_name="example.jpg",
        url="https://example.com/image.jpg",
        direct_url_upload=True  # 启用直接URL上传
    )
    
    print(f"上传成功！访问URL: {result['url']}")
    
    # 示例2：让系统自动从URL推断文件名
    result = await upload_file(
        prefix="docs",
        file_name=None,  # 不指定文件名，系统会从URL推断
        url="https://example.com/document.pdf",
        direct_url_upload=True
    )
    
    print(f"自动文件名上传成功！访问URL: {result['url']}")
    
    # 示例3：带回退机制的上传（推荐）
    # 如果直接URL上传失败，会自动回退到下载后上传的方式
    result = await upload_file(
        prefix="files",
        file_name="backup_file.zip",
        url="https://example.com/file.zip",
        direct_url_upload=True  # 会尝试直接上传，失败则回退
    )
    
    print(f"带回退机制上传成功！访问URL: {result['url']}")

async def example_traditional_upload():
    """传统上传方式（对比）"""
    
    # 传统方式：先下载到本地再上传
    result = await upload_file(
        prefix="images",
        file_name="traditional.jpg",
        url="https://example.com/image.jpg",
        direct_url_upload=False  # 或者不设置这个参数（默认为False）
    )
    
    print(f"传统方式上传成功！访问URL: {result['url']}")

async def example_sas_upload():
    """SAS令牌上传（现有功能，保持不变）"""
    
    # SAS上传方式保持不变
    result = await upload_file(
        prefix="secure",
        file_name="secure_file.txt",
        url="https://example.com/file.txt",
        use_sas=True  # 使用SAS令牌上传
    )
    
    print(f"SAS上传成功！访问URL: {result['url']}")

# 功能特点说明：
"""
1. 直接URL上传的优势：
   - 节省带宽：不需要先下载到本地
   - 提高速度：直接在存储服务之间传输
   - 减少内存使用：不需要在内存中缓存文件内容

2. 自动回退机制：
   - 如果直接URL上传失败，会自动回退到传统的下载后上传方式
   - 确保上传的可靠性

3. 兼容性：
   - 完全兼容现有的SAS上传功能
   - 不影响现有代码的使用

4. 使用建议：
   - 对于大文件，建议使用direct_url_upload=True
   - 对于需要高可靠性的场景，系统会自动处理回退
   - SAS功能保持不变，可以继续正常使用
"""

if __name__ == "__main__":
    # 运行示例（需要先配置好环境）
    # asyncio.run(example_direct_url_upload())
    print("请根据需要取消注释相应的示例代码来运行测试")
