import asyncio
import os
from pathlib import Path
from utils import Tools, load_yaml_config
import aiohttp

async def test_direct_url_upload():
    """测试直接从URL上传功能"""
    # 加载配置
    config_path = Path(__file__).parent / "config" / "setting.yaml"
    Tools.conf = await load_yaml_config(config_path)
    
    # 导入需要在配置加载后进行
    from control.upload import upload_file
    
    # 测试URL（使用一个公开的测试图片）
    test_url = "https://httpbin.org/image/png"
    test_filename = "test_direct_upload.png"
    
    print("开始测试直接URL上传功能...")
    
    try:
        # 测试直接URL上传
        print(f"正在从 {test_url} 直接上传...")
        result = await upload_file(
            prefix="test_direct",
            file_name=test_filename,
            url=test_url,
            direct_url_upload=True
        )
        
        print("直接URL上传成功！")
        print(f"OSS URL: {result['oss_url']}")
        print(f"Blob URL: {result['blob_url']}")
        print(f"访问URL: {result['url']}")
        
        # 验证文件是否可访问
        print("\n开始验证上传的文件...")
        async with aiohttp.ClientSession() as session:
            async with session.get(result['url']) as response:
                if response.status == 200:
                    print("文件验证成功！文件可以正常访问。")
                    print(f"文件大小: {len(await response.read())} bytes")
                else:
                    print(f"文件访问失败，状态码: {response.status}")
        
    except Exception as e:
        print(f"直接URL上传测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())
    
    print("\n" + "="*50)
    
    # 对比测试：传统下载后上传方式
    try:
        print("开始测试传统下载后上传方式（对比）...")
        result_traditional = await upload_file(
            prefix="test_traditional",
            file_name="test_traditional_upload.png",
            url=test_url,
            direct_url_upload=False  # 使用传统方式
        )
        
        print("传统方式上传成功！")
        print(f"OSS URL: {result_traditional['oss_url']}")
        print(f"Blob URL: {result_traditional['blob_url']}")
        print(f"访问URL: {result_traditional['url']}")
        
    except Exception as e:
        print(f"传统方式上传测试失败: {str(e)}")
        import traceback
        print(traceback.format_exc())

async def test_direct_url_upload_with_fallback():
    """测试直接URL上传的回退机制"""
    # 加载配置
    config_path = Path(__file__).parent / "config" / "setting.yaml"
    Tools.conf = await load_yaml_config(config_path)
    
    from control.upload import upload_file
    
    # 使用一个可能不被OSS直接支持的URL来测试回退机制
    test_url = "https://httpbin.org/image/jpeg"
    
    print("测试直接URL上传的回退机制...")
    
    try:
        result = await upload_file(
            prefix="test_fallback",
            file_name="test_fallback.jpg",
            url=test_url,
            direct_url_upload=True
        )
        
        print("上传成功（可能使用了回退机制）！")
        print(f"访问URL: {result['url']}")
        
    except Exception as e:
        print(f"回退测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始测试直接URL上传功能...")
    asyncio.run(test_direct_url_upload())
    
    print("\n" + "="*50)
    print("开始测试回退机制...")
    asyncio.run(test_direct_url_upload_with_fallback())
