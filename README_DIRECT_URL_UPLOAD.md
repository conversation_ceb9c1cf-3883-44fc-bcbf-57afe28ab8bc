# 直接URL上传功能

## 功能概述

新增了OSS和Azure Blob Storage的直接URL上传功能，允许存储服务直接从源URL拉取文件，而不需要先下载到本地服务器。这个功能可以：

- **节省带宽**：不需要通过本地服务器中转
- **提高速度**：直接在存储服务之间传输
- **减少内存使用**：不需要在内存中缓存大文件
- **保持兼容性**：完全兼容现有的SAS上传功能

## API使用方法

### HTTP API

现有的上传接口 `/gpt/api/upload/gpt/image` 新增了 `direct_url_upload` 参数：

```bash
curl -X POST "http://localhost:8000/gpt/api/upload/gpt/image" \
  -F "prefix=images" \
  -F "url=https://example.com/image.jpg" \
  -F "direct_url_upload=true"
```

**参数说明：**
- `prefix`: 文件前缀路径（默认: "imgs"）
- `file_name`: 文件名（可选，会从URL自动推断）
- `url`: 源文件URL
- `data`: 上传的文件数据（与url二选一）
- `direct_url_upload`: 是否启用直接URL上传（默认: false）

### Python代码调用

```python
from control.upload import upload_file

# 直接URL上传
result = await upload_file(
    prefix="images",
    file_name="example.jpg",
    url="https://example.com/image.jpg",
    direct_url_upload=True  # 启用直接URL上传
)

print(f"上传成功！访问URL: {result['url']}")
```

## 功能特点

### 1. 自动回退机制

如果直接URL上传失败（例如OSS不支持某些URL格式），系统会自动回退到传统的下载后上传方式，确保上传的可靠性。

```python
# 即使直接上传失败，也会自动回退到传统方式
result = await upload_file(
    prefix="files",
    url="https://some-complex-url.com/file.zip",
    direct_url_upload=True  # 会尝试直接上传，失败则自动回退
)
```

### 2. 智能文件名推断

如果不指定文件名，系统会从URL自动推断：

```python
result = await upload_file(
    prefix="docs",
    file_name=None,  # 系统会从URL推断文件名
    url="https://example.com/document.pdf",
    direct_url_upload=True
)
```

### 3. 完全兼容现有功能

所有现有的功能保持不变：

```python
# SAS上传功能保持不变
result = await upload_file(
    prefix="secure",
    url="https://example.com/file.txt",
    use_sas=True  # SAS功能不受影响
)

# 传统上传方式保持不变
result = await upload_file(
    prefix="images",
    url="https://example.com/image.jpg"
    # direct_url_upload默认为False
)
```

## 技术实现

### Azure Blob Storage

使用 `start_copy_from_url` 方法进行异步复制：

```python
async def upload_blob_from_url_direct(source_url, pre, file_name, headers):
    blob_client.start_copy_from_url(source_url=source_url)
    # 等待复制完成
    while blob_properties.copy.status == 'pending':
        await asyncio.sleep(1)
```

### 阿里云OSS

尝试使用OSS的copy_object功能，如果不支持则抛出异常触发回退：

```python
async def upload_oss_from_url_direct(source_url, pre, file_name, headers):
    try:
        bucket.copy_object(source_url, object_name)
    except Exception:
        # 触发回退到下载方式
        raise Exception("OSS direct URL copy not supported")
```

## 使用建议

1. **大文件上传**：建议对大文件使用 `direct_url_upload=True`
2. **可靠性要求高**：系统自动处理回退，无需担心失败
3. **现有代码**：无需修改现有代码，新功能是可选的
4. **SAS功能**：继续正常使用，不受影响

## 测试

运行测试文件验证功能：

```bash
python test_direct_url_upload.py
```

## 错误处理

系统会自动处理各种错误情况：

- **直接上传失败**：自动回退到下载方式
- **URL无效**：返回相应错误信息
- **网络问题**：重试机制和超时处理
- **存储服务异常**：详细的错误日志

## 日志

系统会记录详细的操作日志：

```
INFO - OSS direct URL upload failed, falling back to download method
INFO - Blob direct URL upload https://file.302.ai/gpt/test/file.jpg cost 2.34s
```
