#!/usr/bin/env python3
"""
测试超时修复的脚本
"""
import asyncio
import aiohttp
from control.upload import download_file, get_file_name

async def test_download_with_timeout():
    """测试下载功能的超时配置"""
    print("测试下载功能的超时配置...")
    
    # 测试一个小文件的下载
    test_url = "https://httpbin.org/delay/2"  # 这个URL会延迟2秒响应
    
    try:
        # 配置超时设置
        timeout = aiohttp.ClientTimeout(
            total=10,  # 总超时时间10秒
            connect=5,  # 连接超时5秒
            sock_read=5  # 读取超时5秒
        )
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            print(f"开始下载: {test_url}")
            data = await download_file(test_url, session)
            print(f"下载成功，数据大小: {len(data)} bytes")
            
    except asyncio.TimeoutError:
        print("❌ 下载超时 - 这是预期的，因为我们设置了较短的超时时间")
    except Exception as e:
        print(f"❌ 下载失败: {e}")

async def test_get_file_name_with_timeout():
    """测试 get_file_name 函数的超时配置"""
    print("\n测试 get_file_name 函数的超时配置...")
    
    # 测试一个会快速响应的URL
    test_url = "https://httpbin.org/json"
    
    try:
        file_data, file_name, headers = await get_file_name(url=test_url)
        print(f"✅ 获取文件成功:")
        print(f"   文件大小: {len(file_data)} bytes")
        print(f"   文件名: {file_name}")
        print(f"   Headers: {headers}")
        
    except asyncio.TimeoutError:
        print("❌ 获取文件超时")
    except Exception as e:
        print(f"❌ 获取文件失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 开始测试超时修复...")
    
    await test_download_with_timeout()
    await test_get_file_name_with_timeout()
    
    print("\n✅ 测试完成！")
    print("\n修复说明:")
    print("1. 为所有 aiohttp.ClientSession 添加了超时配置")
    print("2. 下载文件时设置了更长的超时时间 (总计5分钟，读取2分钟)")
    print("3. 其他HTTP请求设置了合理的超时时间")
    print("4. 这应该解决了 asyncio.TimeoutError 的问题")

if __name__ == "__main__":
    asyncio.run(main())
